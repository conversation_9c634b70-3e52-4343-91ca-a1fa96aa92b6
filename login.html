
<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Finance</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
        
    <style>
        /* General styles */
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 20px;
            color: #000000;
            line-height: 1.6;
            font-size: 20px;
            -webkit-print-color-adjust: exact; /* Ensure colors are printed correctly */
            -moz-print-color-adjust: exact; /* Ensure colors are printed correctly */
            print-color-adjust: exact; /* Ensure colors are printed correctly */
            background: linear-gradient(to right, #f0f0f0, #ffffff);
            color: #000000; /* Dark red color for text */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            box-sizing: border-box; /* Ensure padding and borders are included in element's total width and height */
            overflow-x: hidden; /* Prevent horizontal scrolling */
            font-size: 20px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            text-align: left; /* Align text to the left */
            padding: 20px; /* Add padding to the body */
            margin: 0; /* Remove default margin */
            background-color: #f8f9fa; /* Light background color */
            color: #000000; /* Dark text color for better contrast */
            text-align: left; /* Align text to the left */
            line-height: 1.5; /* Set line height for readability */
            font-size: 16px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            text :bold; /* Ensure text is not transformed */
            font-family: 'Helvetica Neue', Arial, sans-serif; /* Use a clean sans-serif font */
        }

           @media print {
            .container {
                border: 2px solid #333 !important;   /* Dark outline */
                border-radius: 10px !important;
                margin: 20px auto !important;        /* Margin around the box */
                padding: 16px !important;
                background: #fff !important;
                box-shadow: none !important;
                max-width: 95% !important;
            }
        }
        

        h1,
        h2 {
            text-align: center;
        }

        .section-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 5px 8px;
            font-size: 13px;
            height: 28px;
            width: calc(100% - 20px);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .form-group select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><polygon points="0,0 10,0 5,5" fill="%23222"/></svg>') no-repeat right 10px center;
            background-size: 10px;
        }

        .image-container-box {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 32px;
            margin-bottom: 10px;
        }

        .image-item {
            flex: 0 0 180px;
            min-width: 180px;
            max-width: 200px;
            background: #fff;
            border: 1.5px solid #222;
            border-radius: 10px;
            padding: 12px 8px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .image-item {
            flex: 0 0 180px;
            min-width: 180px;
            max-width: 200px;
            background: #fff;
            border: 1.5px solid #222;
            border-radius: 10px;
            padding: 12px 8px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .image-item {
            flex: 0 0 180px;
            min-width: 180px;
            max-width: 200px;
            background: #fff;
            border: 1.5px solid #222;
            border-radius: 10px;
            padding: 12px 8px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

               .image-item input[type="file"] {
            display: block;
            margin: 0 auto 6px auto;
            width: 95%;
            box-sizing: border-box;
            position: static;
        }
        }

        .image-item label {
            font-weight: bold;
            margin-bottom: 6px;
            width: 100%;
            text-align: left;
            font-size: 16px;
        }

        .file-info {
            font-size: 12px;
            color: #888;
            margin-bottom: 6px;
            width: 100%;
            text-align: center;
            border-radius: 5px;
            background: #f9f9f9;
            padding: 4px 0;
        }

        .image-preview-box {
            margin-top: 6px;
            width: 100%;
            height: 90px;
            min-height: 90px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #fafafa;
            box-shadow: none;
            padding: 0;
        }
        
        #customerPhotoPreview,
.jewelry-preview-img {
    width: 70px !important;
    height: 70px !important;
    object-fit: cover;
    border-radius: 8px;
    background: #fff;
    display: block;
    margin: 0 auto;
}
        .image-preview-box img:hover {
            transform: scale(1.1);
        }

        .image-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border: 1px dashed #ddd;
            border-radius: 6px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            width: 100%;
            height: 100%;
        }

        .image-placeholder:hover {
            border-color: #999;
        }

        .image-placeholder i {
            font-size: 24px;
            color: #999;
            margin-bottom: 10px;
        }

        .image-placeholder span {
            font-size: 14px;
            color: #999;
        }
        .image-placeholder span:hover {
            color: #333;
        }
                /* Add to your <style> section */
        .header-flex {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        .header-logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #eee;
            background: #fff;
        }
        .header-details {
            flex: 1;
            text-align: left;
        }
        .header-details h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
            color: #1900f8;
            text-transform: uppercase;
        }
        .header-details div {
            font-size: 1rem;
            margin-bottom: 4px;
            color: #000000;
            font-weight: bold;
            letter-spacing: 1px;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
               @media print {
    /* Reset page size and margins */
    @page {
        size: A4;
        margin: 5mm;
    }

    /* Basic print setup */
    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 8px !important;
    }

    /* Container adjustments */
    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 5mm 5mm 40mm 5mm !important; /* Extra bottom padding for signatures */
        min-height: 250mm !important; /* Ensure minimum height for A4 */
        position: relative !important;
    }

    /* Header adjustments */
    .header-flex {
        margin-bottom: 3mm !important;
    }
    
    .header-logo img {
        width: 20mm !important;
        height: 20mm !important;
    }

    /* Section spacing */
    .section-box {
        margin-bottom: 2mm !important;
        padding: 2mm !important;
    }

    /* Form elements */
    .form-group {
        margin-bottom: 1mm !important;
    }

    .form-group label {
        margin-bottom: 0.5mm !important;
    }

    /* Image sizes */
    .image-preview-box img,
    #customerPhotoPreview,
    .jewelry-preview-img {
        width: 15mm !important;
        height: 15mm !important;
    }

    /* Weight row adjustments */
    .weight-row {
        gap: 1mm !important;
    }

    .weight-item {
        padding: 1mm !important;
    }

    /* Signature row adjustments */
    .signature-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-top: 40px;
        width: 100%;
        gap: 10px;
    }
    
    .signature-block {
        flex: 1;
        padding: 0 10px;
    }
    
    .signature-label {
        font-weight: bold;
        margin-bottom: 30px;
        font-size: 13px;
        text-align: center;
    }
    
    .signature-line {
        border-bottom: 1px solid #333;
        margin: 30px 0 0 0;
        height: 30px;
        width: 100%;
        display: block;
    }
       @media print {
        .signature-row,
        .signature-block,
        .signature-label,
        .signature-block input[type="text"] {
            display: block !important;
            visibility: visible !important;
            height: auto !important;
            width: 100% !important;
            color: #000 !important;
        }
        .signature-row {
            position: static !important;
            margin-top: 40px !important;
            page-break-inside: avoid !important;
        }
        .signature-block input[type="text"] {
            border: 1px solid #333 !important;
            background: #fff !important;
            margin-top: 8px !important;
            width: 80% !important;
            max-width: 220px !important;
        }
    }
    /* Force single page */
   
    * {
        overflow: visible !important;
        page-break-inside: avoid !important;
        page-break-before: avoid !important;
        page-break-after: avoid !important;
    }

    /* Hide unnecessary elements */
    .hide-on-print,
    .print-options,
    input[type="file"],
    button:not(.print-btn) {
        display: none !important;
    }

    /* Compact spacing */
    h1, h2 {
        margin: 1mm 0 !important;
        font-size: 10px !important;
    }

    input, select, textarea {
        padding: 1mm !important;
        margin: 0.5mm 0 !important;
        height: auto !important;
    }

    body {
        font-size: 10pt !important; /* Adjust as needed */
    }

    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0.25in !important; /* Adjust as needed */
        border: 1px solid #ccc !important;
    }

    .hide-on-print {
        display: none !important;
        visibility: hidden !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        margin: 0 !important;
    }
        .signature-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-top: 40px;
        width: 100%;
    }
    
    .signature-block {
        flex: 1;
        padding: 0 10px;
    }
    
    .signature-block:first-child {
        text-align: left;
    }
    .signature-block:nth-child(2) {
        text-align: center;
    }
    .signature-block:last-child {
        text-align: right;
    }
    
    @media print {
        body {
            font-size: 8px !important; /* Adjust as needed */
        }
        .container {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 5mm !important; /* Adjust as needed */
        }
        .header-flex {
            margin-bottom: 3mm !important;
        }
        .header-logo img {
            width: 20mm !important;
            height: 20mm !important;
        }
        .section-box {
            margin-bottom: 2mm !important;
            padding: 2mm !important;
        }
        .form-group {
            margin-bottom: 1mm !important;
        }
        .form-group label {
            margin-bottom: 0.5mm !important;
        }
        .image-preview-box img,
        #customerPhotoPreview,
        .jewelry-preview-img {
            width: 15mm !important;
            height: 15mm !important;
        }
        .weight-row {
            gap: 1mm !important;
        }
        .weight-item {
            padding: 1mm !important;
        }
        .weight-item label {
            font-size: 7px !important;
        }
        .weight-item input {
            font-size: 7px !important;
        }


            @media print {
            .signature-row,
            .signature-block,
            .signature-label,

            .signature-block input[type="text"] {

                display: block !important;
                visibility: visible !important;
                height: auto !important;
                width: 100% !important;
                color: #000 !important;
            }
            .signature-row {
                position: static !important;
                margin-top: 40px !important;
                page-break-inside: avoid !important;
            }
            .signature-block input[type="text"] {
                border: 1px solid #333 !important;
                background: #fff !important;
                margin-top: 8px !important;
                width: 80% !important;
                max-width: 220px !important;
            }
    /* Add more specific print styles here */
}
    </style>
</head>
<body>
    <!-- Place this inside your .container at the top -->
<div class="header-flex header-center">
    <div class="header-logo">
        <img src="20250608_130850.png" alt="Amman Pawn Shop Logo">
    </div>
    <div class="header-details">
        <h1>AMMAN JEWELERY PAWN SHOP</h1>
        <div><b>REGISTERED OFFICE: NO 2/4-4, S.V.A. EXTENSION - 4, OPPOSITE GOVERNMENT GIRLS HIGH SCHOOL,</b></div>
        <div><b>TIRUCHENGODE - 637211, NAMAKKAL DISTRICT.</b></div>
        <div><b>GST NO: 33AAGFA6262H1Z3</b></div>
        <div><b>PROPRIETOR: M.KARTHICK</b></div>
        <div><b>REGISTERED UNDER THE TAMIL NADU PAWN BROKERS ACT,1943.</b></div>
        <div><b>LICENCE NO: 2020250401145</b></div>
        <div><b>EMAIL: <EMAIL></b></div>
    </div>
</div>

<style>
.header-flex.header-center {
    justify-content: center;
    text-align: center;
    gap: 30px;
}
.header-flex.header-center .header-logo {
    display: flex;
    justify-content: center;
    align-items: center;
}
.header-flex.header-center .header-details {
    text-align: center;
    align-items: center;
    display: flex;
    flex-direction: column;
}
.header-flex.header-center .header-details h1 {
    text-align: center;
}
.header-flex.header-center .header-details div {
    text-align: center;
    display: block;
    width: 100%;
}
@media (max-width: 700px) {
    .header-flex.header-center {
        flex-direction: column;
        gap: 10px;
    }
    .header-flex.header-center .header-logo img {
        margin: 0 auto;
    }
}
</style>
    <div class="container">
        <!-- Customer Information -->
        <div class="section-box">
            <!-- Customer Details -->
            <h2>Customer Details</h2>
            <div class="form-group">
                <label for="name">Full Name:</label>
                <input type="text" id="name" placeholder="John Doe">
            </div>
            <div class="form-group">
                <label for="address">Address:</label>
                <textarea id="address" rows="2" placeholder="123 Gold Street, City"></textarea>
            </div>
            <div class="form-group">
                <label for="contact">Contact Number:</label>
                <input type="tel" id="contact" placeholder="1234567890">
            </div>
            
            <!-- Loan Information Row -->
            <div class="loan-info-row">
                <div class="form-group third-width">
                    <label for="loanAmount">Loan Amount:</label>
                    <input type="number" id="loanAmount" placeholder="0.00" min="0" step="0.01">
                </div>
                <div class="form-group third-width">
                    <label for="loanDate">Loan Date:</label>
                    <input type="date" id="loanDate">
                </div>
                <div class="form-group third-width">
                    <label for="renewalDate">Last Renewal Date:</label>
                    <input type="date" id="renewalDate">
                </div>
            </div>
            <!-- Amount in Words Row -->
            <div class="amount-words-row">
                <div class="form-group">
                    <label>Amount in Words:</label>
                    <div id="loanAmountWords" class="amount-words"></div>
                </div>
            </div>
        </div>

        <!-- Image upload section -->
        <div class="section-box">
            <h2>Images</h2>
            <div class="image-container-box">
                <!-- Customer Photo -->
                <div class="image-item">
                    <label>Customer Photo:</label>
                    <input type="file" id="customerPhotoInput" accept="image/*">
                    <div id="customerFileInfo" class="file-info">No file chosen</div>
                    <div class="image-preview-box">
                        <div id="customerPhotoPlaceholder" class="image-placeholder hide-on-print">
                            <span style="font-size: 7px;">Upload Customer Photo</span>
                        </div>
                        <img id="customerPhotoPreview" alt="Customer Photo" style="display: none;">
                    </div>
                </div>
                
                <!-- Jewelry Photos -->
                <div class="image-item">
                    <label>Jewelry Photos:</label>
                    <input type="file" id="jewelryPhotoInput" accept="image/*" multiple>
                    <div id="jewelryFileInfo" class="file-info">No file chosen</div>
                    <div class="image-preview-box">
                        <div id="jewelryPreviewContainer">
                            <div id="jewelryPhotoPlaceholder" class ="image-placeholder hide-on-print">
                                <span style="font-size: 7px;">Upload Jewelry Images</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Single Jewelry Details section with proper structure -->
        <div class="section-box">
            <h2>Jewelry Details</h2>
            <div id="jewelryItemsList">
                <!-- Initial jewelry item will be added here by JavaScript -->
            </div>
            <!-- Totals calculation row -->
              <div class="jewelry-totals">
                <h3>Totals</h3>
                <div class="weight-row totals-row">
                    <div class="weight-item">
                        <label for="totalJewelryCount">Total Jewelry Count:</label>
                        <input type="number" id="totalJewelryCount" readonly>
                    </div>
                    <div class="weight-item">
                        <label for="totalJewelryWeight">Total Weight (grams):</label>
                        <input type="number" id="totalJewelryWeight" readonly>
                    </div>
                    <div class="weight-item">
                        <label for="totalStoneWeight">Total Stone Weight (grams):</label>
                        <input type="number" id="totalStoneWeight" readonly>
                    </div>
                    <div class="weight-item">
                        <label for="totalNetWeight">Total Net Weight (grams):</label>
                        <input type="number" id="totalNetWeight" readonly>
                    </div>
                </div>
            </div>
            
            <div class="add-item-container">
                <button type="button" id="addJewelryItemBtn" class="add-item-btn hide-on-print">Add Jewelry Item</button>
            </div>
        </div>
              <!-- Place this after the totals section, before </div> of .container -->
     
       
        

        <!-- Print Options -->
    <div class="section-box print-options">
        <h2>Print Options</h2>
        <div class="print-options">
            <label>
                <input type="checkbox" id="printCustomerDetails" checked>
                Print Customer Details
            </label>
        </div>

        <div class="print-options">
            <label>
                <input type="checkbox" id="printJewelryDetails" checked>
                Print Jewelry Details
            </label>
        </div>

        <div class="print-options">
            <label>
                <input type="checkbox" id="printLoanDetails" checked>
                Print Loan Details
            </label>
        </div>

        <div class="print-options">
            <label>
                <input type="checkbox" id="printSignatures" checked>
                Print Signatures
            </label>
        </div>

        <!-- Print button -->
        <button class="print-btn header-center" onclick="printDocument()">Print Document</button>
    </div>
                            <div class="signature-row">
                    <div class="signature-block left">
                        <label class="signature-label" for="BranchManagerSignature">Branch Manager Signature:</label>
                        <input type="text" id="BranchManagerSignature" placeholder="Branch Manager Signature">
                    </div>
                    <div class="signature-block center">
                        <label class="signature-label" for="CashierSignature">Cashier Signature:</label>
                        <input type="text" id="CashierSignature" placeholder="Cashier Signature">
                    </div>
                    <div class="signature-block right">
                        <label class="signature-label" for="CustomerSignature">Customer Signature:</label>
                        <input type="text" id="CustomerSignature" placeholder="Customer Signature">
                    </div>
                </div>
<style>
.signature-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    margin-top: 40px;
    width: 100%;
    gap: 0;
}

.signature-block {
    flex: 1 !important;
    padding: 0 15px;
    min-width: 0;
    width: 33.33%;
    box-sizing: border-box;
}

.signature-block.left {
    text-align: left !important;
    padding-left: 0 !important;
}
.signature-block.center {
    text-align: center !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}
.signature-block.right {
    text-align: right !important;
    padding-right: 0 !important;
}

.signature-block .signature-label {
    display: block !important;
    margin-bottom: 8px !important;
}

.signature-block input[type="text"] {
    width: 100% !important;
    box-sizing: border-box !important;
}

.signature-label {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 14px;
    display: block;
}

.signature-block input[type="text"] {
    width: 100%;
    min-width: 100px;
    box-sizing: border-box;
    max-width: 100%;
    padding: 4px 8px;
    margin-top: 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Print-specific styles */
@media print {
    .signature-row {
        display: flex !important;
        justify-content: space-between !important;
        align-items: flex-end !important;
        margin-top: 30mm !important;
        width: 100% !important;
        position: absolute !important;
        bottom: 15mm !important;
        left: 5mm !important;
        right: 5mm !important;
        page-break-inside: avoid !important;
        gap: 10mm !important;
    }

    .signature-block {
        flex: 1 !important;
        padding: 0 5mm !important;
        text-align: center !important;
        min-width: 0 !important;
        max-width: 33.33% !important;
    }

    .signature-block.left {
        text-align: left !important;
        padding-left: 0 !important;
    }
    .signature-block.center {
        text-align: center !important;
    }
    .signature-block.right {
        text-align: right !important;
        padding-right: 0 !important;
    }

    .signature-label {
        font-weight: bold !important;
        margin-bottom: 15mm !important;
        font-size: 10px !important;
        display: block !important;
    }

    .signature-block input[type="text"] {
        border: none !important;
        border-bottom: 2px solid #333 !important;
        background: transparent !important;
        width: 100% !important;
        text-align: center !important;
        padding: 2mm 0 !important;
        font-size: 9px !important;
        min-height: 8mm !important;
        box-sizing: border-box !important;
    }
}
</style>
 
    <script>
        // Calculate net weight automatically
        function calculateNetWeight() {
            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0;
            const stoneWeight = parseFloat(document.getElementById('stoneWeight').value) || 0;
            const netWeight = totalWeight - stoneWeight;

            if (netWeight >= 0) {
                document.getElementById('netWeight').value = netWeight.toFixed(2);
            }
        }
        // Add event listeners for weight inputs
        
        document.getElementById('jewelryWeight').addEventListener('input', calculateNetWeight);
        document.getElementById('stoneWeight').addEventListener('input', calculateNetWeight);
        // Function to update amount in words   
        function calculateNetWeight() {
            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0;
            const stoneWeight = parseFloat(document.getElementById('stoneWeight').value) || 0;
            const netWeight = totalWeight - stoneWeight;    
            // Update net weight input field

            if (netWeight >= 0) {
                document.getElementById('netWeight').value = netWeight.toFixed(2);
            }
        }

        // Set default date to today for renewal date
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date as default for renewal date
            const today = new Date();
            const formattedDate = today.toISOString().substr(0, 10); // Format: YYYY-MM-DD
            document.getElementById('renewalDate').value = formattedDate;
            
            // Format loan amount with currency symbol on blur
            const loanAmountInput = document.getElementById('loanAmount');
            loanAmountInput.addEventListener('blur', function() {
                if (this.value) {
                    // Format with 2 decimal places
                    const formattedValue = parseFloat(this.value).toFixed(2);
                    this.value = formattedValue;
                    
                    // Update amount in words
                    const amountInWords = numberToWords(parseFloat(this.value));
                    document.getElementById('loanAmountWords').textContent = amountInWords;
                }
            });
            
            // Auto-set renewal date to one year after loan date
            const loanDateInput = document.getElementById('loanDate');
            loanDateInput.addEventListener('change', function() {
                if (this.value) {
                    const loanDate = new Date(this.value);
                    // Add one year to the loan date
                    const renewalDate = new Date(loanDate);
                    renewalDate.setFullYear(renewalDate.getFullYear() + 1);
                    // Format and set the renewal date
                    const renewalDateFormatted = renewalDate.toISOString().substr(0, 10);
                    document.getElementById('renewalDate').value = renewalDateFormatted;
                }
            });
            
            // Customer photo preview
            const customerPhotoInput = document.getElementById('customerPhotoInput');
            const customerPhotoPreview = document.getElementById('customerPhotoPreview');
            const customerPhotoPlaceholder = document.getElementById('customerPhotoPlaceholder');
            const customerFileInfo = document.getElementById('customerFileInfo');
            
            // Check if there's already a file selected (for page reloads)
            if (customerPhotoInput.files && customerPhotoInput.files[0]) {
                handleCustomerPhotoSelection(customerPhotoInput.files[0]);
            }
            
            customerPhotoInput.addEventListener('change', function(e) {
                const file = this.files[0];
                if (file) {
                    handleCustomerPhotoSelection(file);
                } else {
                    customerFileInfo.textContent = 'No file chosen';
                    customerPhotoPreview.style.display = 'none';
                    customerPhotoPlaceholder.style.display = 'flex';
                }
            });
            
            function handleCustomerPhotoSelection(file) {
                customerFileInfo.textContent = file.name;
                const reader = new FileReader();
                reader.onload = function(e) {
                    customerPhotoPreview.src = e.target.result;
                    customerPhotoPreview.style.display = 'block';
                    customerPhotoPlaceholder.style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
            
            // Jewelry photos preview
            const jewelryPhotoInput = document.getElementById('jewelryPhotoInput');
            const jewelryPreviewContainer = document.getElementById('jewelryPreviewContainer');
            const jewelryPhotoPlaceholder = document.getElementById('jewelryPhotoPlaceholder');
            const jewelryFileInfo = document.getElementById('jewelryFileInfo');
            
            // Check if there are already files selected (for page reloads)
            if (jewelryPhotoInput.files && jewelryPhotoInput.files.length > 0) {
                handleJewelryPhotoSelection(jewelryPhotoInput.files);
            }
            
            jewelryPhotoInput.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    handleJewelryPhotoSelection(this.files);
                } else {
                    jewelryFileInfo.textContent = 'No file chosen';
                    
                    // Clear previous previews except placeholder
                    Array.from(jewelryPreviewContainer.children).forEach(child => {
                        if (child !== jewelryPhotoPlaceholder) {
                            jewelryPreviewContainer.removeChild(child);
                        }
                    });
                    
                    jewelryPhotoPlaceholder.style.display = 'flex';
                }
            });
            
            function handleJewelryPhotoSelection(files) {
                jewelryFileInfo.textContent = files.length + ' file(s) selected';
                jewelryPhotoPlaceholder.style.display = 'none';
                
                // Clear previous previews except placeholder
                Array.from(jewelryPreviewContainer.children).forEach(child => {
                    if (child !== jewelryPhotoPlaceholder) {
                        jewelryPreviewContainer.removeChild(child);
                    }
                });
                
                // Add new previews
                Array.from(files).forEach(file => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'jewelry-preview-img';
                        jewelryPreviewContainer.appendChild(img);
                    };
                    reader.readAsDataURL(file);
                });
            }
        });
    </script>
    <script>
document.addEventListener('DOMContentLoaded', function() {
    // Customer Photo Preview
    const customerPhotoInput = document.getElementById('customerPhotoInput');
    const customerPhotoPreview = document.getElementById('customerPhotoPreview');
    const customerPhotoPlaceholder = document.getElementById('customerPhotoPlaceholder');
    const customerFileInfo = document.getElementById('customerFileInfo');

    customerPhotoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            customerFileInfo.textContent = file.name;
            const reader = new FileReader();
            reader.onload = function(e) {
                customerPhotoPreview.src = e.target.result;
                customerPhotoPreview.style.display = 'block';
                customerPhotoPlaceholder.style.display = 'none';
            };
            reader.readAsDataURL(file);
        } else {
            customerFileInfo.textContent = 'No file chosen';
            customerPhotoPreview.style.display = 'none';
            customerPhotoPlaceholder.style.display = 'flex';
        }
    });

    // Jewelry Photos Preview
    const jewelryPhotoInput = document.getElementById('jewelryPhotoInput');
    const jewelryPreviewContainer = document.getElementById('jewelryPreviewContainer');
    const jewelryPhotoPlaceholder = document.getElementById('jewelryPhotoPlaceholder');
    const jewelryFileInfo = document.getElementById('jewelryFileInfo');

    jewelryPhotoInput.addEventListener('change', function() {
        // Remove old previews except placeholder
        Array.from(jewelryPreviewContainer.children).forEach(child => {
            if (child !== jewelryPhotoPlaceholder) {
                jewelryPreviewContainer.removeChild(child);
            }
        });

        if (this.files.length > 0) {
            jewelryFileInfo.textContent = this.files.length + ' file(s) selected';
            jewelryPhotoPlaceholder.style.display = 'none';
            Array.from(this.files).forEach(file => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'jewelry-preview-img';
                    jewelryPreviewContainer.appendChild(img);
                };
                reader.readAsDataURL(file);
            });
        } else {
            jewelryFileInfo.textContent = 'No file chosen';
            jewelryPhotoPlaceholder.style.display = 'flex';
        }
    });
});
</script>
    <style>
        /* Weight row styling */
        .weight-row {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: space-between;
            width: 100%;
            box-sizing: border-box;
        }

        .weight-item {
            flex: 1;
            min-width: 150px;
            max-width: 200px;
            box-sizing: border-box;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            margin-bottom: 10px;
            /* Ensure content doesn't overflow */
            overflow: hidden;
        }

        .weight-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
            font-size: 14px;
            /* Remove duplicate styling */
            width: 100%;
            box-sizing: border-box;
        }

        .weight-item input {
            width: 100%;
            box-sizing: border-box;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            /* Ensure input stays within container */
            max-width: 100%;
        }

        /* Print styles for weight items */
        @media print {
            .weight-row {
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 10px !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }
            
            .weight-item {
                flex: 1 !important;
                min-width: 0 !important;
                max-width: 33% !important;
                box-sizing: border-box !important;
                padding: 5px !important;
            }
            
            .weight-item input {
                width: 100% !important;
                box-sizing: border-box !important;
                padding: 4px !important;
            }
        }

        /* Jewelry type row styling */
        .jewelry-type-row {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
            align-items: flex-end;
        }

        .jewelry-selection, .purity-selection {
            flex: 1;
            min-width: 150px;
        }

        /* Print Options Styles */
                @media print {
            .print-options {
                display: none !important;
                visibility: hidden !important;
                height: 0 !important;
                width: 0 !important;
                overflow: hidden !important;
                position: absolute !important;
                left: -9999px !important;
                top: -9999px !important;
                padding: 0 !important;
                margin: 0 !important;
                border: none !important;
                background: none !important;
            }
        }
                @media print {
            .print-options {
                display: none !important;
                visibility: hidden !important;
                /* ... */
            }
        }

        .print-options label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            font-weight: bold;
        }

        .print-options input[type="checkbox"] {
            margin: 0;
            width: 16px;
            height: 16px;
        }

        /* Hide elements for print based on options */
        .hide-for-print {
            display: none !important;
        }
    </style>
    <script>
        // Wait for DOM to be fully loaded before attaching event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.timeLog("DOM fully loaded - initializing application");
            
            // Initialize customer and loan functionality
            initializeCustomerAndLoanFunctions();
            
            // Initialize jewelry items functionality
            initializeJewelryItemsFunctionality();
            
            // Initialize print functionality
            initializePrintFunctionality();
            
            // Initialize image upload functionality
            initializeImageUploadFunctionality();
        });
        
        // Customer and loan related functions
        function initializeCustomerAndLoanFunctions() {
            // Set today's date as default for renewal date
            const today = new Date();
            const formattedDate = today.toISOString().substr(0, 10); // Format: YYYY-MM-DD
            const renewalDateInput = document.getElementById('renewalDate');
            if (renewalDateInput) {
                renewalDateInput.value = formattedDate;
            }
            
            // Format loan amount with currency symbol on blur
            const loanAmountInput = document.getElementById('loanAmount');
            if (loanAmountInput) {
                loanAmountInput.addEventListener('blur', function() {
                    if (this.value) {
                        // Format with 2 decimal places
                        const formattedValue = parseFloat(this.value).toFixed(2);
                        this.value = formattedValue;
                        
                        // Update amount in words
                        updateAmountInWords(this.value);
                    }
                });
                
                // Also update amount in words on input
                loanAmountInput.addEventListener('input', function() {
                    updateAmountInWords(this.value);
                });
            }
            
            // Auto-set renewal date to one year after loan date
            const loanDateInput = document.getElementById('loanDate');
            if (loanDateInput) {
                loanDateInput.addEventListener('change', function() {
                    if (this.value) {
                        const loanDate = new Date(this.value);
                        // Add one year to the loan date
                        const renewalDate = new Date(loanDate);
                        renewalDate.setFullYear(renewalDate.getFullYear() + 1);
                        // Format and set the renewal date
                        const renewalDateFormatted = renewalDate.toISOString().substr(0, 10);
                        document.getElementById('renewalDate').value = renewalDateFormatted;
                    }
                });
            }
        }
        
        // Function to update amount in words
        function updateAmountInWords(amount) {
            const amountWordsElement = document.getElementById('loanAmountWords');
            if (amountWordsElement && amount) {
                const amountInWords = convertNumberToWords(parseFloat(amount));
                amountWordsElement.textContent = amountInWords;
            }
        }
        
        // Function to convert number to words
        function convertNumberToWords(num) {
            const units = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
            const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
            
            function convertWholeNumber(num) {
                if (num === 0) return 'Zero';
                
                let words = '';
                // Handle crores (10,000,000s)
                if (num >= 10000000) {
                    words += convertWholeNumber(Math.floor(num / 10000000)) + ' Crore ';
                    num %= 10000000;
                }       
                // Handle arab (100,000,000s)
                if (num >= 100000000) {
                    words += convertWholeNumber(Math.floor(num / 100000000)) + ' Arab ';
                    num %= 100000000;
                }
                // Handle kharab (1,000,000,000s)
                if (num >= 1000000000) {
                    words += convertWholeNumber(Math.floor(num / 1000000000)) + ' Kharab ';
                    num %= 1000000000;
                }

                // Handle lakhs (100,000s)
                if (num >= 100000) {
                    words += convertWholeNumber(Math.floor(num / 100000)) + ' Lakh ';
                    num %= 100000;
                }
                
                // Handle thousands
                if (num >= 1000) {
                    words += convertWholeNumber(Math.floor(num / 1000)) + ' Thousand ';
                    num %= 1000;
                }
                
                // Handle hundreds
                if (num >= 100) {
                    words += convertWholeNumber(Math.floor(num / 100)) + ' Hundred ';
                    num %= 100;
                }
                
                // Handle tens and units
                if (num > 0) {
                    // Add 'and' if there's a preceding hundreds/thousands/etc.
                    if (words !== '') {
                        words += 'and ';
                    }
                    
                    if (num < 20) {
                        words += units[num];
                    } else {
                        words += tens[Math.floor(num / 10)];
                        if (num % 10 > 0) {
                            words += '-' + units[num % 10];
                        }
                    }
                }
                
                return words;
            }
            
            // Split number into whole and decimal parts
            const wholePart = Math.floor(num);
            const decimalPart = Math.round((num - wholePart) * 100);
            
            let result = convertWholeNumber(wholePart) + ' Rupees';
            
            // Add decimal part if exists
            if (decimalPart > 0) {
                result += ' and ' + convertWholeNumber(decimalPart) + ' Paise';
            }
            
            return result + ' Only';
        }
        
        
        // Jewelry items functionality
        function initializeJewelryItemsFunctionality() {
            // Create the initial jewelry item
            createInitialJewelryItem();
            
            // Add event listener to the Add Jewelry Item button
            const addJewelryItemBtn = document.getElementById('addJewelryItemBtn');
            if (addJewelryItemBtn) {
                addJewelryItemBtn.addEventListener('click', function() {
                    console.log("Add Jewelry Item button clicked");
                    addJewelryItem();
                });
            } else {
                console.error("Add Jewelry Item button not found");
            }
            
            // Calculate initial totals
            calculateTotals();
        }
        
        // Function to create the initial jewelry item
        function createInitialJewelryItem() {
            const jewelryItemsList = document.getElementById('jewelryItemsList');
            if (!jewelryItemsList) {
                console.error("Jewelry items list container not found");
                return;
            }
            
            // Clear any existing items first
            jewelryItemsList.innerHTML = '';
            
            const initialItem = document.createElement('div');
            initialItem.className = 'jewelry-item';
            initialItem.innerHTML = `
                <div class="weight-row">
                    <div class="weight-item">
                        <label for="jewelryCount_0">Jewelry Count:</label>
                        <input type="number" id="jewelryCount_0" class="jewelry-count" min="1" value="1">
                    </div>

                    <div class="weight-item">
                        <label for="jewelryWeight_0">Gross Weight (grams):</label>
                        <input type="number" id="jewelryWeight_0" class="jewelry-weight" min="0" step="0.1">
                        <label for
                        <input type="number" id="jewelryWeight_0" class="jewelry-weight" min="0" step="0.1">
                        </div>

                    <div class="weight-item">
                        <label for="stoneWeight_0">Stone Weight (grams):</label>
                        <input type="number" id="stoneWeight_0" class="stone-weight" min="0" step="0.1">
                    </div>

                    <div class="weight-item">
                        <label for="netWeight_0">Net Weight (grams):</label>
                        <input type="number" id="netWeight_0" class="net-weight" min="0" step="0.1" readonly>
                    </div>
                </div>

                <div class="jewelry-type-row">
                    <div class="jewelry-selection">
                        <label for="jewelryType_0">Jewelry Type:</label>
                        <select id="jewelryType_0" class="jewelry-type">
                           <option value="">Select Jewelry Type</option>
                            <option value="gold-chain">Gold Chain</option>
                            <option value="gold-ring">Gold Ring</option>
                            <option value="gold-bracelet">Gold Bracelet</option>
                            <option value="gold-necklace">Gold Necklace</option>
                            <option value="gold-earrings">Gold Earrings</option>
                            <option value="gold-bangles">Gold Bangles</option>
                            <option value="gold-pendants">Gold Pendants</option>
                            <option value="gold-ear-studs">Gold Ear Studs</option>
                            <option value="gold-studs">Gold Studs</option>
                            <option value="gold-stone studs ">Gold Stone Studs</option>
                            <option value="gold-mattal">Gold Mattal</option>
                            <option value="gold-baby studs">Gold Baby Studs</option>
                            <option value="gold-lockets">Gold Lockets</option>
                            <option value="gold-anklets">Gold Anklets</option>
                            <option value="gold-manga pich">Gold Manga Pich</option>
                            <option value="gold-talikasu">Gold Talikasu </option>
                            <option value="gold-nose pin">Gold Nose Pin</option>
                            <option value="gold-thali kundu">Gold Thali Kundu</option>
                            <option value="gold-stud and jimikki">Gold Stud and Jimikki</option>
                            <option value="gold-gold coin">Gold Coin</option>
                            <option value="silver-items">Silver Items</option>
                        </select>
                    </div>

                    <div class="purity-selection">
                        <label for="purityType_0">Purity Type:</label>
                        <select id="purityType_0" class="purity-type">
                            <option value="">Select Purity</option>
                            <option value="24k">24K (99.9%)</option>
                            <option value="22k">22K (91.6%)</option>
                            <option value="18k">18K (75.0%)</option>
                            <option value="14k">14K (58.3%)</option>
                            <option value="10k">10K (41.7%)</option>
                        </select>
                    </div>
                    
                    <div class="item-actions">
                        <button type="button" class="remove-item-btn hide-on-print" data-index="0" style="display: none;">Remove</button>
                    </div>
                </div>
            `;
            
            jewelryItemsList.appendChild(initialItem);
            
            // Initialize net weight calculation for the initial item
            initializeNetWeightCalculation(0);
        }
        
        // Function to add a new jewelry item
        function addJewelryItem() {
            const jewelryItemsList = document.getElementById('jewelryItemsList');
            if (!jewelryItemsList) return;

            const itemCount = jewelryItemsList.querySelectorAll('.jewelry-item').length;
            const newIndex = itemCount;

            const newItem = document.createElement('div');
            newItem.className = 'jewelry-item';
            newItem.innerHTML = `
                <div class="weight-row">
                    <div class="weight-item">
                        <label for="jewelryCount_${newIndex}">Jewelry Count:</label>
                        <input type="number" id="jewelryCount_${newIndex}" class="jewelry-count" min="1" value="1">
                    </div>
                    <div class="weight-item">
                        <label for="jewelryWeight_${newIndex}">Gross Weight (grams):</label>
                        <input type="number" id="jewelryWeight_${newIndex}" class="jewelry-weight" min="0" step="0.1">
                    </div>
                    <div class="weight-item">
                        <label for="stoneWeight_${newIndex}">Stone Weight (grams):</label>
                        <input type="number" id="stoneWeight_${newIndex}" class="stone-weight" min="0" step="0.1">
                    </div>
                    <div class="weight-item">
                        <label for="netWeight_${newIndex}">Net Weight (grams):</label>
                        <input type="number" id="netWeight_${newIndex}" class="net-weight" min="0" step="0.1" readonly>
                    </div>
                </div>
                <div class="jewelry-type-row">
                    <div class="jewelry-selection">
                        <label for="jewelryType_${newIndex}">Jewelry Type:</label>
                        <select id="jewelryType_${newIndex}" class="jewelry-type">
                            <option value="">Select Jewelry Type</option>
                            <option value="gold-chain">Gold Chain</option>
                            <option value="gold-ring">Gold Ring</option>
                            <option value="gold-bracelet">Gold Bracelet</option>
                            <option value="gold-necklace">Gold Necklace</option>
                            <option value="gold-earrings">Gold Earrings</option>
                            <option value="gold-bangles">Gold Bangles</option>
                            <option value="gold-pendants">Gold Pendants</option>
                            <option value="gold-ear-studs">Gold Ear Studs</option>
                            <option value="gold-studs">Gold Studs</option>
                            <option value="gold-stone studs ">Gold Stone Studs</option>
                            <option value="gold-mattal">Gold Mattal</option>
                            <option value="gold-baby studs">Gold Baby Studs</option>
                            <option value="gold-lockets">Gold Lockets</option>
                            <option value="gold-anklets">Gold Anklets</option>
                            <option value="gold-manga pich">Gold Manga Pich</option>
                            <option value="gold-talikasu">Gold Talikasu </option>
                            <option value="gold-nose pin">Gold Nose Pin</option>
                            <option value="gold-thali kundu">Gold Thali Kundu</option>
                            <option value="gold-stud and jimikki">Gold Stud and Jimikki</option>
                            <option value="gold-gold coin">Gold Coin</option>
                            <option value="silver-items">Silver Items</option>
                        </select>
                    </div>
                    <div class="purity-selection">
                        <label for="purityType_${newIndex}">Purity Type:</label>
                        <select id="purityType_${newIndex}" class="purity-type">
                            <option value="">Select Purity</option>
                            <option value="24k">24K (99.9%)</option>
                            <option value="22k">22K (91.6%)</option>
                            <option value="18k">18K (75.0%)</option>
                            <option value="14k">14K (58.3%)</option>
                            <option value="10k">10K (41.7%)</option>
                        </select>
                    </div>
                    <div class="item-actions">
                        <button type="button" class="remove-item-btn hide-on-print" data-index="${newIndex}">Remove</button>
                    </div>
                </div>
            `;

            jewelryItemsList.appendChild(newItem);

            // Hide remove button only for the first item
            const removeBtn = newItem.querySelector('.remove-item-btn');
            if (removeBtn) {
                if (newIndex === 0) {
                    removeBtn.style.display = 'none';
                } else {
                    removeBtn.style.display = 'inline-block';
                }
                removeBtn.addEventListener('click', function() {
                    removeJewelryItem(newIndex);
                });
            }

            // Attach event listeners for calculation
            newItem.querySelector('.jewelry-count').addEventListener('input', calculateTotals);
            newItem.querySelector('.jewelry-weight').addEventListener('input', calculateTotals);
            newItem.querySelector('.stone-weight').addEventListener('input', calculateTotals);

            // Initialize net weight calculation for the new item
            initializeNetWeightCalculation(newIndex);

            // Show the remove button on the first item if we have more than one item
            if (jewelryItemsList.querySelectorAll('.jewelry-item').length > 1) {
                const firstItemRemoveBtn = jewelryItemsList.querySelector('.jewelry-item:first-child .remove-item-btn');
                if (firstItemRemoveBtn) {
                    firstItemRemoveBtn.style.display = 'inline-block';
                }
            }

            calculateTotals();
        }
        
        // Function to remove a jewelry item
        function removeJewelryItem(index) {
            console.log(`Removing jewelry item with index: ${index}`);
            
            const jewelryItemsList = document.getElementById('jewelryItemsList');
            const items = jewelryItemsList.querySelectorAll('.jewelry-item');
            
            // Don't remove if it's the last item
            if (items.length <= 1) {
                console.log("Cannot remove the last item");
                return;
            }
            
            console.log(`Total items before removal: ${items.length}`);
            console.log(`Attempting to remove item with index: ${index}`);
            // Check if the index is valid
            if (index < 0 || index >= items.length) {
                console.error(`Invalid index: ${index}. Cannot remove item.`);
                return;
            }
            console.log(`Valid index: ${index}. Proceeding with removal.`);
            // Log the current items for debugging
            items.forEach((item, i) => {
                console.log(`Item ${i}:`, item);
            });
            
            console.log(`Items before removal: ${items.length}`);
            // Log the remove button for debugging
            const removeBtn = document.querySelector(`.remove-item-btn[data-index="${index}"]`);
            if (removeBtn) {
                console.log("Remove button found:", removeBtn);
            } else {
                console.error(`Remove button with index ${index} not found`);
            }
            // Log the jewelry items list for debugging
            console.log("Jewelry items list:", jewelryItemsList);
            // Log the items in the jewelry items list
            console.log("Items in jewelry items list:", items);
                console.log(`Items in jewelry items list: ${items.length}`);
            // Log the jewelry items list container
            console.log("Jewelry items list container:", jewelryItemsList);
            // Log the current state of the jewelry items list
            console.log("Current jewelry items list state:", jewelryItemsList.innerHTML);
            // Find the item with the matching index and remove it
            for (let i = 0; i < items.length; i++) {
                const removeBtn = items[i].querySelector('.remove-item-btn');
                if (removeBtn && removeBtn.getAttribute('data-index') == index) {
                    items[i].remove();
                    console.log(`Item with index ${index} removed`);
                    break;
                } else {
                    console.error(`Remove button with index ${index} not found`);
                    console.error(`Remove button with index ${index} not found`);
                    removeBtn.classList.add('hide-on-print');
                    removeBtn.style.display = 'none';
                }
            }
            
            // Hide the remove button on the first item if we only have one item left
            if (jewelryItemsList.querySelectorAll('.jewelry-item').length === 1) {
                const firstItemRemoveBtn = document.querySelector('.jewelry-item:first-child .remove-item-btn');
                if (firstItemRemoveBtn) {
                    firstItemRemoveBtn.style.display = 'none';
                    console.log("First item remove button hidden");
                    firstItemRemoveBtn.classList.add('hide-on-print');
                } else {
                    console.error("First item remove button not found");
                    firstItemRemoveBtn.classList.add('hide-on-print');
                    firstItemRemoveBtn.style.display = 'none';
                    firstItemRemoveBtn.classList.add('hide-on-print');
                    
                }
            }
            
            // Calculate totals after removing an item
            calculateTotals();
        }
        
        // Function to initialize net weight calculation
        function initializeNetWeightCalculation(index) {
            const jewelryWeightInput = document.getElementById(`jewelryWeight_${index}`);
            const stoneWeightInput = document.getElementById(`stoneWeight_${index}`);
            const netWeightInput = document.getElementById(`netWeight_${index}`);
            
            if (!jewelryWeightInput || !stoneWeightInput || !netWeightInput) {
                console.error(`Could not find weight inputs for index ${index}`);
                return;
            }
            
            // Function to calculate net weight
            function calculateNetWeight() {
                const jewelryWeight = parseFloat(jewelryWeightInput.value) || 0;
                const stoneWeight = parseFloat(stoneWeightInput.value) || 0;
                const netWeight = jewelryWeight - stoneWeight;
                netWeightInput.value = netWeight > 0 ? netWeight.toFixed(1) : 0;
                
                // Update totals whenever an individual weight changes
                calculateTotals();
            }
            
            // Add event listeners for weight inputs
            jewelryWeightInput.addEventListener('input', calculateNetWeight);
            stoneWeightInput.addEventListener('input', calculateNetWeight);
        }
        
        // Function to calculate totals from all jewelry items
        function calculateTotals() {
            console.log("Calculating totals");
            let totalJewelryCount = 0;
            let totalJewelryWeight = 0;
            let totalStoneWeight = 0;
            let totalNetWeight = 0;
            
            // Get all jewelry items
            const jewelryItems = document.querySelectorAll('.jewelry-item');
            
             // Calculate totals
    jewelryItems.forEach(item => {
        const jewelryCount = parseFloat(item.querySelector('.jewelry-count')?.value) || 0;
        const jewelryWeight = parseFloat(item.querySelector('.jewelry-weight')?.value) || 0;
        const stoneWeight = parseFloat(item.querySelector('.stone-weight')?.value) || 0;
        const netWeight = parseFloat(item.querySelector('.net-weight')?.value) || 0;

        totalJewelryCount += jewelryCount;
        totalJewelryWeight += jewelryWeight;
        totalStoneWeight += stoneWeight;
        totalNetWeight += netWeight;
    });
            
          // Update total fields
    document.getElementById('totalJewelryCount').value = totalJewelryCount;
    document.getElementById('totalJewelryWeight').value = totalJewelryWeight.toFixed(1);
    document.getElementById('totalStoneWeight').value = totalStoneWeight.toFixed(1);
    document.getElementById('totalNetWeight').value = totalNetWeight.toFixed(1);

    console.log(`Totals calculated - Count: ${totalJewelryCount}, Jewelry: ${totalJewelryWeight}, Stone: ${totalStoneWeight}, Net: ${totalNetWeight}`);
}
        
        // Print functionality
        function initializePrintFunctionality() {
            // First, remove any existing event listeners by replacing all buttons
            const printBtns = document.querySelectorAll('.print-btn');
            printBtns.forEach(btn => {
                // Create a completely new button
                const newBtn = document.createElement('button');
                newBtn.className = 'print-btn';
                newBtn.textContent = 'Print Receipt';
                newBtn.type = 'button';
                newBtn.id = 'printBtn';
                newBtn.style.display = 'inline-block';
                newBtn.style.margin = '10px 0';
                newBtn.style.padding = '10px 20px';
                newBtn.style.backgroundColor = '#4CAF50';
                newBtn.style.color = 'white';
                newBtn.style.border = 'chocolate 1px solid';
                newBtn.style.borderRadius = '5px';
                newBtn.style.cursor = 'pointer';
                newBtn.style.fontSize = '16px';
                newBtn.style.fontWeight = 'bold';
                newBtn.style.textAlign = 'center';
                
                // Replace the old button
                if (btn.parentNode) {
                    btn.parentNode.replaceChild(newBtn, btn);
                }
            });
            
            // Now add a single event listener to the new button
            const newPrintBtn = document.querySelector('.print-btn');
            if (newPrintBtn) {
                // Use a named function for clarity
                newPrintBtn.addEventListener('click', singlePrintFunction);
            }
            
            // Remove any inline onclick attributes from the HTML
            document.querySelectorAll('[onclick*="print"]').forEach(el => {
                el.removeAttribute('onclick');
            });
        }
        
        // Single print function - this is the ONLY function that should call window.print()
        let isPrinting = false; // Flag to prevent multiple calls
        function singlePrintFunction(e) {
            if (e) e.preventDefault();

            if (isPrinting) return;
            isPrinting = true;

            try {
                // Get all print options
                const printCustomerDetails = document.getElementById('printCustomerDetails')?.checked ?? true;
                const printJewelryDetails = document.getElementById('printJewelryDetails')?.checked ?? true;
                const printLoanDetails = document.getElementById('printLoanDetails')?.checked ?? true;
                const printSignatures = document.getElementById('printSignatures')?.checked ?? true;

                // Get the sections
                const customerSection = document.querySelector('.section-box:nth-of-type(1)'); // Customer Details section
                const imagesSection = document.querySelector('.section-box:nth-of-type(2)'); // Images section
                const jewelrySection = document.querySelector('.section-box:nth-of-type(3)'); // Jewelry Details section
                const signatureRows = document.querySelectorAll('.signature-row'); // All signature rows

                // Apply hide-for-print class based on options
                if (customerSection) {
                    if (!printCustomerDetails) {
                        customerSection.classList.add('hide-for-print');
                    } else {
                        customerSection.classList.remove('hide-for-print');
                    }
                }

                if (jewelrySection) {
                    if (!printJewelryDetails) {
                        jewelrySection.classList.add('hide-for-print');
                    } else {
                        jewelrySection.classList.remove('hide-for-print');
                    }
                }

                // Handle signature sections
                signatureRows.forEach(signatureRow => {
                    if (!printSignatures) {
                        signatureRow.classList.add('hide-for-print');
                    } else {
                        signatureRow.classList.remove('hide-for-print');
                    }
                });

                // Format loan amount for printing
                const loanAmount = document.getElementById('loanAmount').value;
                if (loanAmount) {
                    try {
                        const amountInWords = convertNumberToWords(parseFloat(loanAmount));
                        document.getElementById('loanAmountWords').textContent = amountInWords;
                    } catch (e) {
                        console.error("Error converting amount to words:", e);
                        document.getElementById('loanAmountWords').textContent = "Amount in words not available";
                    }
                }

                // Add A4 class to body for print
                document.body.classList.add('a4-print');

                // Trigger the print dialog after a short delay
                setTimeout(function() {
                    window.print();

                    // Reset after printing
                    setTimeout(function() {
                        document.body.classList.remove('a4-print');
                        isPrinting = false;

                        // Clean up any temporary elements
                        document.querySelectorAll('.temp-print-element').forEach(el => el.remove());

                        // Reset any classes that were modified for printing
                        if (customerSection && !printCustomerDetails) {
                            customerSection.classList.remove('hide-for-print');
                        }
                        if (jewelrySection && !printJewelryDetails) {
                            jewelrySection.classList.remove('hide-for-print');
                        }
                        signatureRows.forEach(signatureRow => {
                            if (!printSignatures) {
                                signatureRow.classList.remove('hide-for-print');
                            }
                        });
                    }, 1000);
                }, 100);
            } catch (error) {
                console.error("Error in print function:", error);
                alert("There was an error with the print function. Please try again.");
                isPrinting = false;
            }
            return false;
        }
    </script>
    
    <style>
        /* Images container and boxes styling */
        .images-container {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .image-box {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: #f9f9f9;
        }
        
        .customer-box {
            flex-basis: 40%;
        }
        
        .jewelry-box {
            flex-basis: 60%;
        }
        
        .box-header {
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        
        .box-content {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        /* Standardized image styling for both customer and jewelry */
        .image-preview-container, .jewelry-placeholder {
            margin-top: 10px;
            text-align: center;
            position: relative;
            width: 50px;
            height: 50px;
        }
        
        #customerPhotoPreview, .jewelry-preview-img {
            width: 50px;
            height: 50px;
            object-fit: cover;
            background-color: #fff;
        }
        
        /* Jewelry images container */
        #jewelryPreviewContainer {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
            justify-content: center;
        }
        
        /* Print styles for the image boxes */
        @media print {
            .image-box {
                flex:  30%;
                max-width: 30%;
                border: none !important;
                padding: 0 !important;
                background: none !important;
                box-shadow: none !important;
            }
            .customer-box, .jewelry-box {
                flex-basis: 10%;
                max-width: 10%;
                flex-basis: 10%;
                max-width: 10%;
                flex-basis: 10%;
                max-width: 10%;
            }
            .box-header {
                font-size: 14px;
                margin-bottom: 10px;
            }
            .box-content {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .image-preview-container, .jewelry-placeholder {
                width: 12px;
                height: 12px;
            }
                     /* Make customer and jewelry preview images larger */
            #customerPhotoPreview,
            .jewelry-preview-img {
                width: 120px !important;
                height: 120px !important;
                object-fit: cover;
                border-radius: 8px;
                border: 1px solid #ccc;
                background: #fff;
            }
            #jewelryPreviewContainer {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 10px;
                justify-content: center;    
                width: 100%;
                max-width: 100%;
                flex-direction: column;
                align-items: center;

            }
            .images-container {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5px;
                justify-content: center;
                align-items: center;
                margin-bottom: 15px;
                
            }
            
            .image-box {
                flex: 1;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                background-color: #f9f9f9;
                flex:  15%;
                max-width: 15%;
                margin-bottom: 20px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                align-items: center;
                border-radius: 5px;
                box-shadow: none;
                padding: 0;
                margin: 0;
                border: none;
                background: none;

            }
            
            .box-header {
                font-weight: bold;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #eee;
                font-size: 14px;
            }
            .box-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-top: 10px;
            }
            .image-preview-container, .jewelry-placeholder {
                margin-top: 10px;
                text-align: center;
                position: relative;
                width: 120px;
                height: 120px;
            }
            #customerPhotoPreview, .jewelry-preview-img {
                width: 120px;
                height: 120px;
                object-fit: cover;
                background-color: #fff;
            }
            /* Hide file input and other elements that should not be printed */
                        .image-item input[type="file"] {
                display: block;
                margin: 0 auto 6px auto;
                width: 95%;
                box-sizing: border-box;
                position: static;
            }
                        input[type="file"] {
                width: 110px;
                overflow: hidden;
            }
            
            input[type="file"]::file-selector-button {
                /* Style your button here if needed */
            }
            
            input[type="file"]::-ms-value {
                display: none;
            }
            
            input[type="file"] {
                display: none; /* Hide file input */
                visibility: hidden; /* Ensure it doesn't take up space */
                position: absolute; /* Remove from document flow */
                left: -9999px; /* Move off-screen */
                top: -9999px; /* Move off-screen */
            }
            .hide-on-print {
                visibility: hidden !important;
                position: absolute !important;
                left: -9999px !important;
                top: -9999px !important;
                /* Ensure the element is completely hidden */
                width: 0 !important;
                height: 0 !important;
                overflow: hidden !important;
                opacity: 0 !important;
                pointer-events: none !important; /* Prevent interaction */
                /* Additional styles to ensure it doesn't affect layout */
                margin: 0 !important;
                padding: 0 !important;
                border: none !important;
                background: none !important;
            }
            
            
            }
        /* General styles */
        html,
        body {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-size: 16px;
            line-height: 1.5;
        }

        html {
            height: 10%;
            font-size: 16px; /* Base font size */
            scroll-behavior: smooth; /* Smooth scrolling */
            background-color: #f4f4f4; /* Light background color */
            color: #2505b4; /* Default text color */
            font-family: Arial, sans-serif;
            overflow-x: hidden; /* Prevent horizontal scroll */
            position: relative; /* For absolute positioning of child elements */
            padding: 0;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: top 20px;
            padding  20px;
        }
        
        .container {
            position: relative;
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);  
            background: rgb(247, 245, 245);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-height: 100vh;
            justify-content: space-between;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);            
        }
        
        h1, h2 {
            text-align: center;
            color: #cf0808;   
            margin-bottom: 20px;
            font-size: 24px;
            font-weight: bold;
            text-transform: uppercase;
                       letter-spacing: 1px;
            margin-top: 0;
            line-height: 1.2;
            font-family: 'Arial', sans-serif;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            
        }
        
        .section-box {
            margin-bottom: 10px;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
               }
        
        .form-group {
            margin-bottom: 8px;
        }
        
        .form-group label {
            margin-bottom: 2px;
            font-size: 13px;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            padding: 5px 8px;
            font-size: 13px;
            height: 28px;
            width: calc(100% - 20px);
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .form-group select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><polygon points="0,0 10,0 5,5" fill="%23222"/></svg>') no-repeat right 10px center;
            background-size: 10px;
        }

                .image-container-box {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 40px;
            margin-bottom: 10px;
        }
        
        .image-item {
            width: 260px;
            min-width: 220px;
            max-width: 280px;
            background: #fff;
            border: 1.5px solid #222;
            border-radius: 10px;
            padding: 16px 10px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .image-item label {
            font-weight: bold;
            margin-bottom: 6px;
            width: 100%;
            text-align: left;
            font-size: 16px;
        }
        
        .file-info {
            font-size: 12px;
            color: #888;
            margin-bottom: 6px;
            width: 100%;
            text-align: center;
            border-radius: 5px;
            background: #f9f9f9;
            padding: 4px 0;
        }
        
        .image-preview-box {
            margin-top: 10px;
            width: 120px;
            height: 120px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #fafafa;
            box-shadow: none;
            padding: 0;
        }
        
        #customerPhotoPreview,
        .jewelry-preview-img {
            width: 100px !important;
            height: 100px !important;
            object-fit: cover;
            border-radius: 8px;
            background: #fff;
            display: block;
        }
}
        .image-preview-box img:hover {
            transform: scale(1.1);
        }

        .image-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border: 1px dashed #ddd;
            border-radius: 6px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            width: 100%;
            height: 100%;
        }

        .image-placeholder:hover {
            border-color: #999;
        }

        .image-placeholder i {
            font-size: 24px;
            color: #999;
            margin-bottom: 10px;
        }

        .image-placeholder span {
            font-size: 14px;
            color: #999;
        }
        .image-placeholder span:hover {
            color: #333;
        }
                /* Add to your <style> section */
        .header-flex {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        .header-logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #eee;
            background: #fff;
        }
        .header-details {
            flex: 1;
            text-align: left;
        }
        .header-details h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
            color: #1900f8;
            text-transform: uppercase;
        }
        .header-details div {
            font-size: 1rem;
            margin-bottom: 4px;
            color: #000000;
            font-weight: bold;
            letter-spacing: 1px;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
               @media print {
    /* Reset page size and margins */
    @page {
        size: A4;
        margin: 5mm;
    }

    /* Basic print setup */
    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 8px !important;
    }

    /* Container adjustments */
    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 5mm !important;
        min-height: auto !important;
    }

    /* Header adjustments */
    .header-flex {
        margin-bottom: 3mm !important;
    }
    
    .header-logo img {
        width: 20mm !important;
        height: 20mm !important;
    }

    /* Section spacing */
    .section-box {
        margin-bottom: 2mm !important;
        padding: 2mm !important;
    }

    /* Form elements */
    .form-group {
        margin-bottom: 1mm !important;
    }

    .form-group label {
        margin-bottom: 0.5mm !important;
    }

    /* Image sizes */
    .image-preview-box img,
    #customerPhotoPreview,
    .jewelry-preview-img {
        width: 15mm !important;
        height: 15mm !important;
    }

    /* Weight row adjustments */
    .weight-row {
        gap: 1mm !important;
    }

    .weight-item {
        padding: 1mm !important;
    }

    /* Signature row adjustments */
    .signature-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-top: 40px;
        width: 100%;
        gap: 10px;
    }
    
    .signature-block {
        flex: 1;
        padding: 0 10px;
    }
    .signature-block:first-child {
        text-align: left;
    }
    .signature-block:nth-child(2) {
        text-align: center;
    }
    .signature-block:last-child {
        text-align: right;
    }
    .signature-label {
        font-weight: bold;
        margin-bottom: 30px;
        font-size: 10px !important;
        text-align: center;
    }
    .signature-line {
        border-bottom: 1px solid #333;
        margin: 30px 0 0 0;
        height: 30px;
        width: 100%;
        display: block;
    }
    
    .signature-label {
        font-weight: bold;
        margin-bottom: 30px;
        font-size: 13px;
        text-align: center;
    }
    
    .signature-line {
        border-bottom: 1px solid #333;
        margin: 30px 0 0 0;
        height: 30px;
        width: 100%;
        display: block;
    }
       @media print {
        .signature-row,
        .signature-block,
        .signature-label,
        .signature-block input[type="text"] {
            display: block !important;
            visibility: visible !important;
            height: auto !important;
            width: 100% !important;
            color: #000 !important;
        }
        .signature-row {
            position: static !important;
            margin-top: 40px !important;
            page-break-inside: avoid !important;
        }
        .signature-block input[type="text"] {
            border: 1px solid #333 !important;
            background: #fff !important;
            margin-top: 8px !important;
            width: 80% !important;
            max-width: 220px !important;
        }
    }
    /* Force single page */
   
    * {
        overflow: visible !important;
        page-break-inside: avoid !important;
        page-break-before: avoid !important;
        page-break-after: avoid !important;
    }

    /* Hide unnecessary elements */
    .hide-on-print,
    .print-options,
    input[type="file"],
    button:not(.print-btn) {
        display: none !important;
    }

    /* Compact spacing */
    h1, h2 {
        margin: 1mm 0 !important;
        font-size: 10px !important;
    }

    input, select, textarea {
        padding: 1mm !important;
        margin: 0.5mm 0 !important;
        height: auto !important;
    }

    body {
        font-size: 10pt !important; /* Adjust as needed */
    }

    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0.25in !important; /* Adjust as needed */
        border: 1px solid #ccc !important;
    }

    .hide-on-print {
        display: none !important;
        visibility: hidden !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        margin: 0 !important;
    }
        .signature-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-top: 40px;
        width: 100%;
    }
    
    .signature-block {
        flex: 1;
        padding: 0 10px;
    }
    
    .signature-block:first-child {
        text-align: left;
    }
    .signature-block:nth-child(2) {
        text-align: center;
    }
    .signature-block:last-child {
        text-align: right;
    }
    
    @media print {
        body {
            font-size: 8px !important; /* Adjust as needed */
        }
        .container {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 5mm !important; /* Adjust as needed */
        }
        .header-flex {
            margin-bottom: 3mm !important;
        }
        .header-logo img {
            width: 20mm !important;
            height: 20mm !important;
        }
        .section-box {
            margin-bottom: 2mm !important;
            padding: 2mm !important;
        }
        .form-group {
            margin-bottom: 1mm !important;
        }
        .form-group label {
            margin-bottom: 0.5mm !important;
        }
        .image-preview-box img,
        #customerPhotoPreview,
        .jewelry-preview-img {
            width: 15mm !important;
            height: 15mm !important;
        }
        .weight-row {
            gap: 1mm !important;
        }
        .weight-item {
            padding: 1mm !important;
        }
        .weight-item label {
            font-size: 7px !important;
        }
        .weight-item input {
            font-size: 7px !important;
        }
        .signature-row {
            display: flex !important;
            justify-content: space-between !important;
            align-items: flex-end !important;
            margin-top: 40px !important;
            width: 100% !important;
        }
        .signature-block {
            flex: 1 !important;
            padding: 0 10px !important;
        }
        .signature-block:first-child {
            text-align: left !important;
        }
        .signature-block:nth-child(2) {
            text-align: center !important;
        }
        .signature-block:last-child {
            text-align: right !important;
        }
        /* Signature row adjustments */
              .signature-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-top: 40px;
            width: 100%;
            gap: 10px;
        }
        
        .signature-block {
            flex: 1;
            padding: 0 10px;
        }
        
        .signature-label {
            font-weight: bold;
            margin-bottom: 30px;
            font-size: 13px;
            text-align: center;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            margin: 30px 0 0 0;
            height: 30px;
            width: 100%;
            display: block;
        }
            @media print {
            .signature-row,
            .signature-block,
            .signature-label,
            .signature-block input[type="text"] {
                display: block !important;
                visibility: visible !important;
                height: auto !important;
                width: 100% !important;
                color: #000 !important;
            }
            .signature-row {
                position: static !important;
                margin-top: 40px !important;
                page-break-inside: avoid !important;
            }
            .signature-block input[type="text"] {
                border: 1px solid #333 !important;
                background: #fff !important;
                margin-top: 8px !important;
                width: 80% !important;
                max-width: 220px !important;
            }
    /* Add more specific print styles here */
}
    </style>
</head>
